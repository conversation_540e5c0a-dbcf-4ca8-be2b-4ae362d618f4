@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #0dd2ec;
  --secondary: #50ff39;
  --background: #0a0a0a;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-poppins;
    background-color: var(--background);
  }

  html {
    scroll-behavior: smooth;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-primary;
  }

  /* Reduced motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@layer utilities {
  .font-orbitron {
    font-family: "Orbitron", monospace;
  }

  .font-poppins {
    font-family: "Poppins", sans-serif;
  }
}
