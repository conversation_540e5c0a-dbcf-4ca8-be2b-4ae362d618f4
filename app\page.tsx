"use client"

import { useState } from "react"
import Header from "@/components/header"
import About from "@/components/about"
import Projects from "@/components/projects"
import Skills from "@/components/skills"
import Contact from "@/components/contact"
import Footer from "@/components/footer"
import ParticleBackground from "@/components/particle-background"

export default function Portfolio() {
  const [skillsVisible, setSkillsVisible] = useState(false)

  const toggleSkills = () => {
    setSkillsVisible(!skillsVisible)
  }

  return (
    <div className="min-h-screen relative">
      <ParticleBackground />
      <div className="container relative z-10 mx-auto max-w-6xl px-4 py-8 md:px-8">
        <Header />
        <main>
          <About />
          <Projects />
          <Skills isVisible={skillsVisible} onToggle={toggleSkills} />
          <Contact />
        </main>
        <Footer />
      </div>
    </div>
  )
}
