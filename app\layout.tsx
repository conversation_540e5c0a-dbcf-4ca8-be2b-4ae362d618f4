import type React from "react"
import type { Metada<PERSON> } from "next"
import { Orbitron, Poppins } from "next/font/google"
import "./globals.css"

const orbitron = Orbitron({
  subsets: ["latin"],
  weight: ["400", "700", "900"],
  variable: "--font-orbitron",
  display: "swap",
})

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
  display: "swap",
})

export const metadata: Metadata = {
  title: "JMD_Development | Developer Portfolio",
  description: "Personal portfolio of JMD_Development - Aspiring developer creating responsive, user-friendly websites",
  keywords: ["developer", "portfolio", "web development", "frontend", "React", "JavaScript"],
  authors: [{ name: "JMD_Development" }],
  viewport: "width=device-width, initial-scale=1",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${orbitron.variable} ${poppins.variable}`}>
      <body className="font-poppins antialiased">{children}</body>
    </html>
  )
}
