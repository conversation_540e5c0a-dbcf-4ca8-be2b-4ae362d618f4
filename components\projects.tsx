import { ExternalLink } from "lucide-react"

const projects = [
  {
    id: 1,
    title: "Responsive Business Landing Page",
    description: "A responsive landing page for a local business with modern design principles.",
    link: "fp/index-new.html",
    technologies: ["HTML", "CSS", "JavaScript"],
  },
  {
    id: 2,
    title: "Interactive E-commerce Site",
    description: "An interactive website with a shop, contact form and a pong game.",
    link: "JTech/index.html",
    technologies: ["HTML", "CSS", "JavaScript"],
  },
  {
    id: 3,
    title: "Task Manager Application",
    description: "An interactive task manager application currently in development.",
    link: "task/public/login.html",
    technologies: ["HTML", "CSS", "JavaScript"],
  },
  {
    id: 4,
    title: "Cafe Landing Page",
    description: "A responsive landing page for a local business with dark/light mode toggle.",
    link: "https://cafe-lilac.vercel.app",
    technologies: ["HTML", "CSS", "JavaScript"],
    external: true,
  },
]

export default function Projects() {
  return (
    <section className="mb-16" id="projects" aria-labelledby="projects-heading">
      <h2
        id="projects-heading"
        className="text-2xl md:text-3xl font-bold mb-8 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent font-orbitron"
      >
        My Projects
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {projects.map((project) => (
          <article
            key={project.id}
            className="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/15 transition-all duration-300 hover:transform hover:scale-105 border border-white/10"
          >
            <h3 className="text-xl font-semibold mb-3 text-white">{project.title}</h3>
            <p className="text-gray-300 mb-4 leading-relaxed flex-grow">{project.description}</p>
            <div className="flex flex-wrap gap-2 mb-4">
              {project.technologies.map((tech) => (
                <span
                  key={tech}
                  className="px-2 py-1 bg-primary/20 text-primary text-xs rounded-full border border-primary/30"
                >
                  {tech}
                </span>
              ))}
            </div>
            <a
              href={project.link}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-primary hover:text-secondary transition-colors duration-300 font-medium"
              aria-label={`View ${project.title} project`}
            >
              View Project
              <ExternalLink size={16} />
            </a>
          </article>
        ))}
      </div>
    </section>
  )
}
