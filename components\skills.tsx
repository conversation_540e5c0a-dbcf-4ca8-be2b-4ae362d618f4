"use client"

import { ChevronDown, ChevronUp } from "lucide-react"

interface SkillsProps {
  isVisible: boolean
  onToggle: () => void
}

const skills = ["HTML", "CSS", "JavaScript", "React", "TypeScript", "Next.js", "Tailwind CSS", "Node.js"]

export default function Skills({ isVisible, onToggle }: SkillsProps) {
  return (
    <section className="mb-16" id="skills" aria-labelledby="skills-heading">
      <button
        onClick={onToggle}
        onMouseEnter={() => !isVisible && onToggle()}
        className="flex items-center gap-2 text-2xl md:text-3xl font-bold mb-6 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent font-orbitron hover:opacity-80 transition-opacity duration-300 cursor-pointer"
        aria-expanded={isVisible}
        aria-controls="skills-content"
        id="skills-heading"
      >
        Skills
        {isVisible ? <ChevronUp size={24} /> : <ChevronDown size={24} />}
      </button>

      <div
        id="skills-content"
        className={`transition-all duration-500 ease-in-out overflow-hidden ${
          isVisible ? "max-h-96 opacity-100 transform translate-y-0" : "max-h-0 opacity-0 transform -translate-y-4"
        }`}
        aria-hidden={!isVisible}
      >
        <div className="flex flex-wrap gap-3 pt-2">
          {skills.map((skill, index) => (
            <span
              key={skill}
              className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full text-white border border-white/20 hover:bg-primary/20 hover:border-primary/40 transition-all duration-300 hover:transform hover:scale-105"
              style={{
                animationDelay: isVisible ? `${index * 100}ms` : "0ms",
              }}
            >
              {skill}
            </span>
          ))}
        </div>
      </div>
    </section>
  )
}
